package com.dorsey.core.service.dataload;

import com.dorsey.core.enums.DataSet;
import com.dorsey.core.exception.ServiceException;
import com.dorsey.core.model.lov.DataSetLov;
import com.dorsey.core.service.dataload.processors.LogoProcessor;
import com.dorsey.core.service.dataload.processors.OHProcessor;
import com.dorsey.core.service.dataload.processors.RProcessor;
import com.dorsey.core.service.dataload.processors.UProcessor;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.multipart.MultipartFile;
import org.xml.sax.SAXException;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CoreFileProcessorServiceTest {

    @Mock
    private LogoProcessor logoProcessor;

    @Mock
    private RProcessor rProcessor;

    @Mock
    private OHProcessor ohProcessor;

    @Mock
    private UProcessor uProcessor;

    @Mock
    private MultipartFile file;

    @Mock
    private DataSetLov dataSetLov;

    @InjectMocks
    private CoreFileProcessorService coreFileProcessorService;

    private InputStream inputStream;

    @BeforeEach
    void setUp() throws IOException {
        inputStream = new ByteArrayInputStream("test data".getBytes());
        when(file.getInputStream()).thenReturn(inputStream);
        when(dataSetLov.getCode()).thenReturn("TEST");
    }

    @Test
    void testProcessLogoDataSet() throws Exception {
        // Given
        try (MockedStatic<DataSet> mockedDataSet = Mockito.mockStatic(DataSet.class)) {
            mockedDataSet.when(() -> DataSet.fromLov(dataSetLov)).thenReturn(DataSet.LOGO);

            // When
            coreFileProcessorService.process(dataSetLov, file);

            // Then
            verify(logoProcessor).process(any(InputStream.class));
            verifyNoInteractions(rProcessor, ohProcessor, uProcessor);
            mockedDataSet.verify(() -> DataSet.fromLov(dataSetLov));
        }
    }

    @Test
    void testProcessRDataSet() throws Exception {
        // Given
        try (MockedStatic<DataSet> mockedDataSet = Mockito.mockStatic(DataSet.class)) {
            mockedDataSet.when(() -> DataSet.fromLov(dataSetLov)).thenReturn(DataSet.R);

            // When
            coreFileProcessorService.process(dataSetLov, file);

            // Then
            verify(rProcessor).process(any(InputStream.class));
            verifyNoInteractions(logoProcessor, ohProcessor, uProcessor);
            mockedDataSet.verify(() -> DataSet.fromLov(dataSetLov));
        }
    }

    @Test
    void testProcessOHDataSet() throws Exception {
        // Given
        try (MockedStatic<DataSet> mockedDataSet = Mockito.mockStatic(DataSet.class)) {
            mockedDataSet.when(() -> DataSet.fromLov(dataSetLov)).thenReturn(DataSet.OH);

            // When
            coreFileProcessorService.process(dataSetLov, file);

            // Then
            verify(ohProcessor).process(any(InputStream.class));
            verifyNoInteractions(logoProcessor, rProcessor, uProcessor);
            mockedDataSet.verify(() -> DataSet.fromLov(dataSetLov));
        }
    }

    @Test
    void testProcessUDataSet() throws Exception {
        // Given
        try (MockedStatic<DataSet> mockedDataSet = Mockito.mockStatic(DataSet.class)) {
            mockedDataSet.when(() -> DataSet.fromLov(dataSetLov)).thenReturn(DataSet.U);

            // When
            coreFileProcessorService.process(dataSetLov, file);

            // Then
            verify(uProcessor).process(any(InputStream.class));
            verifyNoInteractions(logoProcessor, rProcessor, ohProcessor);
            mockedDataSet.verify(() -> DataSet.fromLov(dataSetLov));
        }
    }

    @Test
    void testProcessWithNullDataSet() {
        // Given
        try (MockedStatic<DataSet> mockedDataSet = Mockito.mockStatic(DataSet.class)) {
            mockedDataSet.when(() -> DataSet.fromLov(dataSetLov)).thenReturn(null);

            // When
            coreFileProcessorService.process(dataSetLov, file);

            // Then
            verifyNoInteractions(logoProcessor, rProcessor, ohProcessor, uProcessor);
            mockedDataSet.verify(() -> DataSet.fromLov(dataSetLov));
        }
    }

    @Test
    void testProcessWithIOException() throws Exception {
        // Given
        IOException ioException = new IOException("File read error");
        when(file.getInputStream()).thenThrow(ioException);

        try (MockedStatic<DataSet> mockedDataSet = Mockito.mockStatic(DataSet.class)) {
            mockedDataSet.when(() -> DataSet.fromLov(dataSetLov)).thenReturn(DataSet.LOGO);

            // When & Then
            ServiceException exception = assertThrows(ServiceException.class,
                () -> coreFileProcessorService.process(dataSetLov, file));

            assertEquals("File read error", exception.getMessage());
            assertEquals(ioException, exception.getCause());

            // Verify that DataSet.fromLov was called
            mockedDataSet.verify(() -> DataSet.fromLov(dataSetLov));
        }
    }

    @Test
    void testProcessWithInvalidFormatException() throws Exception {
        // Given
        InvalidFormatException formatException = new InvalidFormatException("Invalid format");

        try (MockedStatic<DataSet> mockedDataSet = Mockito.mockStatic(DataSet.class)) {
            mockedDataSet.when(() -> DataSet.fromLov(dataSetLov)).thenReturn(DataSet.LOGO);
            doThrow(formatException).when(logoProcessor).process(any(InputStream.class));

            // When & Then
            ServiceException exception = assertThrows(ServiceException.class,
                () -> coreFileProcessorService.process(dataSetLov, file));

            assertEquals("Invalid format", exception.getMessage());
            assertEquals(formatException, exception.getCause());

            // Verify interactions
            verify(logoProcessor).process(any(InputStream.class));
            mockedDataSet.verify(() -> DataSet.fromLov(dataSetLov));
        }
    }

    @Test
    void testProcessWithSAXException() throws Exception {
        // Given
        SAXException saxException = new SAXException("XML parsing error");

        try (MockedStatic<DataSet> mockedDataSet = Mockito.mockStatic(DataSet.class)) {
            mockedDataSet.when(() -> DataSet.fromLov(dataSetLov)).thenReturn(DataSet.LOGO);
            doThrow(saxException).when(logoProcessor).process(any(InputStream.class));

            // When & Then
            ServiceException exception = assertThrows(ServiceException.class,
                () -> coreFileProcessorService.process(dataSetLov, file));

            assertEquals("XML parsing error", exception.getMessage());
            assertEquals(saxException, exception.getCause());

            // Verify interactions
            verify(logoProcessor).process(any(InputStream.class));
            mockedDataSet.verify(() -> DataSet.fromLov(dataSetLov));
        }
    }

    @Test
    void testProcessInputStreamIsClosed() throws Exception {
        // Given
        InputStream spyInputStream = spy(inputStream);
        when(file.getInputStream()).thenReturn(spyInputStream);

        try (MockedStatic<DataSet> mockedDataSet = Mockito.mockStatic(DataSet.class)) {
            mockedDataSet.when(() -> DataSet.fromLov(dataSetLov)).thenReturn(DataSet.LOGO);

            // When
            coreFileProcessorService.process(dataSetLov, file);

            // Then
            verify(spyInputStream).close();
        }
    }

    @Test
    void testProcessWithUnknownDataSet() {
        // Given
        when(dataSetLov.getCode()).thenReturn("UNKNOWN");

        try (MockedStatic<DataSet> mockedDataSet = Mockito.mockStatic(DataSet.class)) {
            // Mock DataSet.fromLov to return null for unknown dataset
            mockedDataSet.when(() -> DataSet.fromLov(dataSetLov)).thenReturn(null);

            // When
            coreFileProcessorService.process(dataSetLov, file);

            // Then
            verifyNoInteractions(logoProcessor, rProcessor, ohProcessor, uProcessor);
        }
    }

    @Test
    void testProcessWithValidDataSetLov() throws Exception {
        // Given
        when(dataSetLov.getCode()).thenReturn("LOGO");

        try (MockedStatic<DataSet> mockedDataSet = Mockito.mockStatic(DataSet.class)) {
            mockedDataSet.when(() -> DataSet.fromLov(dataSetLov)).thenReturn(DataSet.LOGO);

            // When
            coreFileProcessorService.process(dataSetLov, file);

            // Then
            verify(logoProcessor).process(any(InputStream.class));
            verifyNoInteractions(rProcessor, ohProcessor, uProcessor);
        }
    }
}
