package com.dorsey.core.service.dataload;

import com.dorsey.core.enums.DataSet;
import com.dorsey.core.exception.ServiceException;
import com.dorsey.core.model.lov.DataSetLov;
import com.dorsey.core.service.dataload.processors.LogoProcessor;
import com.dorsey.core.service.dataload.processors.OHProcessor;
import com.dorsey.core.service.dataload.processors.RProcessor;
import com.dorsey.core.service.dataload.processors.UProcessor;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.web.multipart.MultipartFile;
import org.xml.sax.SAXException;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class CoreFileProcessorServiceTest {

    @Mock
    private LogoProcessor logoProcessor;

    @Mock
    private RProcessor rProcessor;

    @Mock
    private OHProcessor ohProcessor;

    @Mock
    private UProcessor uProcessor;

    @Mock
    private MultipartFile file;

    @Mock
    private DataSetLov dataSetLov;

    @InjectMocks
    private CoreFileProcessorService coreFileProcessorService;

    private InputStream inputStream;

    @BeforeEach
    void setUp() throws IOException {
        inputStream = new ByteArrayInputStream("test data".getBytes());
        when(file.getInputStream()).thenReturn(inputStream);
    }

    @Test
    void testProcessLogoDataSet() throws Exception {
        // Given
        when(dataSetLov.getCode()).thenReturn("LOGO");

        // When
        coreFileProcessorService.process(dataSetLov, file);

        // Then
        verify(logoProcessor).process(any(InputStream.class));
        verifyNoInteractions(rProcessor, ohProcessor, uProcessor);
    }

    @Test
    void testProcessRDataSet() throws Exception {
        // Given
        when(dataSetLov.getCode()).thenReturn("R");

        // When
        coreFileProcessorService.process(dataSetLov, file);

        // Then
        verify(rProcessor).process(any(InputStream.class));
        verifyNoInteractions(logoProcessor, ohProcessor, uProcessor);
    }

    @Test
    void testProcessOHDataSet() throws Exception {
        // Given
        when(dataSetLov.getCode()).thenReturn("OH");

        // When
        coreFileProcessorService.process(dataSetLov, file);

        // Then
        verify(ohProcessor).process(any(InputStream.class));
        verifyNoInteractions(logoProcessor, rProcessor, uProcessor);
    }

    @Test
    void testProcessUDataSet() throws Exception {
        // Given
        when(dataSetLov.getCode()).thenReturn("U");

        // When
        coreFileProcessorService.process(dataSetLov, file);

        // Then
        verify(uProcessor).process(any(InputStream.class));
        verifyNoInteractions(logoProcessor, rProcessor, ohProcessor);
    }

    @Test
    void testProcessWithNullDataSet() {
        // Given
        when(dataSetLov.getCode()).thenReturn("UNKNOWN");

        // When
        coreFileProcessorService.process(dataSetLov, file);

        // Then
        verifyNoInteractions(logoProcessor, rProcessor, ohProcessor, uProcessor);
    }

    @Test
    void testProcessWithIOException() throws Exception {
        // Given
        IOException ioException = new IOException("File read error");
        when(file.getInputStream()).thenThrow(ioException);
        when(dataSetLov.getCode()).thenReturn("LOGO");

        // When & Then
        ServiceException exception = assertThrows(ServiceException.class,
            () -> coreFileProcessorService.process(dataSetLov, file));

        assertEquals("File read error", exception.getMessage());
        assertEquals(ioException, exception.getCause());
    }

    @Test
    void testProcessWithInvalidFormatException() throws Exception {
        // Given
        InvalidFormatException formatException = new InvalidFormatException("Invalid format");
        when(dataSetLov.getCode()).thenReturn("LOGO");
        doThrow(formatException).when(logoProcessor).process(any(InputStream.class));

        // When & Then
        ServiceException exception = assertThrows(ServiceException.class,
            () -> coreFileProcessorService.process(dataSetLov, file));

        assertEquals("Invalid format", exception.getMessage());
        assertEquals(formatException, exception.getCause());

        // Verify interactions
        verify(logoProcessor).process(any(InputStream.class));
    }

    @Test
    void testProcessWithSAXException() throws Exception {
        // Given
        SAXException saxException = new SAXException("XML parsing error");
        when(dataSetLov.getCode()).thenReturn("LOGO");
        doThrow(saxException).when(logoProcessor).process(any(InputStream.class));

        // When & Then
        ServiceException exception = assertThrows(ServiceException.class,
            () -> coreFileProcessorService.process(dataSetLov, file));

        assertEquals("XML parsing error", exception.getMessage());
        assertEquals(saxException, exception.getCause());

        // Verify interactions
        verify(logoProcessor).process(any(InputStream.class));
    }

    @Test
    void testProcessInputStreamIsClosed() throws Exception {
        // Given
        InputStream spyInputStream = spy(inputStream);
        when(file.getInputStream()).thenReturn(spyInputStream);
        when(dataSetLov.getCode()).thenReturn("LOGO");

        // When
        coreFileProcessorService.process(dataSetLov, file);

        // Then
        verify(spyInputStream).close();
    }

    @Test
    void testProcessWithUnknownDataSet() {
        // Given
        when(dataSetLov.getCode()).thenReturn("UNKNOWN");

        // When
        coreFileProcessorService.process(dataSetLov, file);

        // Then
        verifyNoInteractions(logoProcessor, rProcessor, ohProcessor, uProcessor);
    }

    @Test
    void testProcessWithNullDataSetLov() {
        // Given
        DataSetLov nullDataSetLov = null;

        // When & Then - DataSet.fromLov(null) will throw NPE when calling dataSetLov.getCode()
        assertThrows(NullPointerException.class, () -> {
            coreFileProcessorService.process(nullDataSetLov, file);
        });

        // Verify no processors were called since exception occurs before processor selection
        verifyNoInteractions(logoProcessor, rProcessor, ohProcessor, uProcessor);
    }

    @Test
    void testProcessWithNullFile() {
        // Given
        when(dataSetLov.getCode()).thenReturn("LOGO");
        MultipartFile nullFile = null;

        // When & Then - file.getInputStream() will throw NPE
        assertThrows(NullPointerException.class, () -> {
            coreFileProcessorService.process(dataSetLov, nullFile);
        });

        // Verify no processors were called since exception occurs before processor call
        verifyNoInteractions(logoProcessor, rProcessor, ohProcessor, uProcessor);
    }

    @Test
    void testProcessWithEmptyDataSetCode() {
        // Given
        when(dataSetLov.getCode()).thenReturn("");

        // When
        coreFileProcessorService.process(dataSetLov, file);

        // Then
        verifyNoInteractions(logoProcessor, rProcessor, ohProcessor, uProcessor);
    }

    @Test
    void testProcessWithNullDataSetCode() {
        // Given
        when(dataSetLov.getCode()).thenReturn(null);

        // When
        coreFileProcessorService.process(dataSetLov, file);

        // Then
        verifyNoInteractions(logoProcessor, rProcessor, ohProcessor, uProcessor);
    }

    @Test
    void testProcessWithCaseSensitiveDataSetCode() {
        // Given
        when(dataSetLov.getCode()).thenReturn("logo"); // lowercase

        // When
        coreFileProcessorService.process(dataSetLov, file);

        // Then - should not match because DataSet enum is case-sensitive
        verifyNoInteractions(logoProcessor, rProcessor, ohProcessor, uProcessor);
    }
}
