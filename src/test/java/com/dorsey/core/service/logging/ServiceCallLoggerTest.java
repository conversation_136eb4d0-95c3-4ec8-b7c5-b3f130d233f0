package com.dorsey.core.service.logging;

import com.dorsey.core.annotation.NoLogResponse;
import com.dorsey.core.dto.Message;
import com.dorsey.core.enums.MessageLevel;
import com.dorsey.core.exception.AuthorizationException;
import com.dorsey.core.exception.ResourceException;
import com.dorsey.core.exception.ServiceException;
import com.dorsey.core.model.logging.ServiceCallLog;
import com.dorsey.core.model.user.Users;
import com.dorsey.core.util.UserUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.ByteArrayInputStream;
import java.lang.reflect.Method;
import java.util.UUID;

import com.dorsey.core.util.HostNameUtil;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ServiceCallLoggerTest {

    @Mock
    private LoggingService loggingService;

    @Mock
    private UserUtil userUtil;

    @Mock
    private ContentCachingRequestWrapper request;

    @Mock
    private ContentCachingResponseWrapper response;

    @InjectMocks
    private ServiceCallLogger serviceCallLogger;

    private Users mockUser;
    private UUID userId;
    private MockedStatic<HostNameUtil> hostNameUtilMock;

    @BeforeEach
    void setUp() {
        userId = UUID.randomUUID();
        mockUser = Users.builder()
                .userId(userId)
                .email("<EMAIL>")
                .build();

        // Mock static HostNameUtil
        hostNameUtilMock = Mockito.mockStatic(HostNameUtil.class);
        hostNameUtilMock.when(HostNameUtil::getHostName).thenReturn("test-host");
    }

    @AfterEach
    void tearDown() {
        // Clean up static mock
        if (hostNameUtilMock != null) {
            hostNameUtilMock.close();
        }
    }

    @Test
    void testInit() {
        // When
        serviceCallLogger.init();

        // Then
        // Verify that a new log entry is created and initialized
        assertDoesNotThrow(() -> serviceCallLogger.init());
    }

    @Test
    void testGetRESTParametersWithoutNoLogResponse() throws NoSuchMethodException {
        // Given
        String className = "TestController";
        Method method = TestController.class.getMethod("testMethod");
        
        serviceCallLogger.init();

        // When
        serviceCallLogger.getRESTParameters(className, method);

        // Then
        // Verify method was called without exceptions
        assertDoesNotThrow(() -> serviceCallLogger.getRESTParameters(className, method));
    }

    @Test
    void testGetRESTParametersWithNoLogResponse() throws NoSuchMethodException {
        // Given
        String className = "TestController";
        Method method = TestController.class.getMethod("testMethodWithNoLogResponse");
        
        serviceCallLogger.init();

        // When
        serviceCallLogger.getRESTParameters(className, method);

        // Then
        // Verify method was called without exceptions
        assertDoesNotThrow(() -> serviceCallLogger.getRESTParameters(className, method));
    }

    @Test
    void testHandleErrorWithServiceException() {
        // Given
        ServiceException exception = new ServiceException("Test service exception");
        serviceCallLogger.init();

        // When
        Message result = serviceCallLogger.handleError(exception);

        // Then
        assertNotNull(result);
        assertEquals(MessageLevel.ERROR, result.getMessageLevel());
        assertTrue(result.getMessage().contains("Test service exception"));
    }

    @Test
    void testHandleErrorWithAuthorizationException() {
        // Given
        AuthorizationException exception = new AuthorizationException();
        serviceCallLogger.init();

        // When & Then
        assertThrows(AuthorizationException.class,
            () -> serviceCallLogger.handleError(exception));
    }

    @Test
    void testHandleErrorWithResourceException() {
        // Given
        ResourceException exception = new ResourceException("Resource not found");
        serviceCallLogger.init();

        // When
        Message result = serviceCallLogger.handleError(exception);

        // Then
        assertNotNull(result);
        assertEquals(MessageLevel.ERROR, result.getMessageLevel());
        assertTrue(result.getMessage().contains("Resource not found"));
    }

    @Test
    void testHandleErrorWithGenericException() {
        // Given
        RuntimeException exception = new RuntimeException("Generic runtime exception");
        serviceCallLogger.init();

        // When
        Message result = serviceCallLogger.handleError(exception);

        // Then
        assertNotNull(result);
        assertEquals(MessageLevel.ERROR, result.getMessageLevel());
        assertTrue(result.getMessage().contains("Generic runtime exception"));
    }

    @Test
    void testFinish() throws Exception {
        // Given
        when(userUtil.getCurrentUser()).thenReturn(mockUser);
        when(request.getMethod()).thenReturn("GET");
        when(request.getHeaderNames()).thenReturn(java.util.Collections.emptyEnumeration());
        when(request.getParameterMap()).thenReturn(java.util.Collections.emptyMap());
        when(request.getServletPath()).thenReturn("/test");
        when(request.getContentAsByteArray()).thenReturn("test request".getBytes());
        when(request.getContentType()).thenReturn("application/json");
        when(request.getHeader("Accept")).thenReturn("application/json");
        when(request.getRemoteAddr()).thenReturn("127.0.0.1");
        when(response.getHeaderNames()).thenReturn(java.util.Collections.emptyList());
        when(response.getStatus()).thenReturn(200);
        when(response.getContentInputStream()).thenReturn(new ByteArrayInputStream("test response".getBytes()));

        serviceCallLogger.init();

        // When
        serviceCallLogger.finish(request, response);

        // Then
        verify(loggingService).save(any(ServiceCallLog.class));
    }

    @Test
    void testConstructorInjection() {
        // Given & When
        ServiceCallLogger logger = new ServiceCallLogger(loggingService, userUtil);

        // Then
        assertNotNull(logger);
        // Verify that the logger was created successfully
        assertDoesNotThrow(() -> logger.init());
    }

    @Test
    void testGetResponseCode() {
        // Given
        serviceCallLogger.init();
        serviceCallLogger.setResponseCode(HttpStatus.OK);

        // When
        int responseCode = serviceCallLogger.getResponseCode();

        // Then
        assertEquals(200, responseCode);
    }

    @Test
    void testSetResponseObj() {
        // Given
        serviceCallLogger.init();
        String responseObj = "test response";

        // When
        serviceCallLogger.setResponseObj(responseObj);

        // Then
        assertDoesNotThrow(() -> serviceCallLogger.setResponseObj(responseObj));
    }

    @Test
    void testSetUserName() {
        // Given
        serviceCallLogger.init();
        String userName = "testUser";

        // When
        serviceCallLogger.setUserName(userName);

        // Then
        assertDoesNotThrow(() -> serviceCallLogger.setUserName(userName));
    }

    @Test
    void testHandleErrorWithNullException() {
        // Given
        serviceCallLogger.init();

        // When
        Message result = serviceCallLogger.handleError(null);

        // Then
        assertNull(result);
        // Verify that response code is set to INTERNAL_SERVER_ERROR even with null exception
        assertEquals(500, serviceCallLogger.getResponseCode());
    }

    @Test
    void testInitializesLogEntryProperly() {
        // When
        serviceCallLogger.init();

        // Then
        assertDoesNotThrow(() -> {
            int responseCode = serviceCallLogger.getResponseCode();
            // Should not throw exception and should have default response code
            assertTrue(responseCode >= 0);
        });
    }

    @Test
    void testSetResponseCodeWithHttpStatus() {
        // Given
        serviceCallLogger.init();

        // When
        serviceCallLogger.setResponseCode(HttpStatus.CREATED);

        // Then
        assertEquals(201, serviceCallLogger.getResponseCode());
    }

    @Test
    void testMultipleInitCallsHandledProperly() {
        // When
        serviceCallLogger.init();
        serviceCallLogger.setResponseCode(HttpStatus.OK);
        int firstResponseCode = serviceCallLogger.getResponseCode();

        serviceCallLogger.init(); // Second init call
        int secondResponseCode = serviceCallLogger.getResponseCode();

        // Then
        assertEquals(200, firstResponseCode);
        // After second init, response code should be reset
        assertNotEquals(200, secondResponseCode);
    }

    // Test controller class for testing method annotations
    public static class TestController {
        public void testMethod() {
            // Test method without annotations
        }

        @NoLogResponse
        public void testMethodWithNoLogResponse() {
            // Test method with NoLogResponse annotation
        }
    }
}
